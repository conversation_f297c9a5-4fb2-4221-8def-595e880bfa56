# Admin Login System - <PERSON><PERSON><PERSON>r <PERSON><PERSON>

## Overview
A complete admin authentication system has been created for the NGO website with secure login functionality, session management, and route protection.

## Features
- ✅ Secure admin login form with validation
- ✅ Session-based authentication
- ✅ Password toggle functionality
- ✅ Error and success message handling
- ✅ Route protection middleware
- ✅ Automatic logout functionality
- ✅ Responsive design with Hindi language support

## Admin Credentials
**Default Login Credentials:**
- **Username:** `admin`
- **Password:** `admin123`

*Note: These credentials are stored in the `.env` file and can be changed by updating the `ADMIN_USERNAME` and `ADMIN_PASSWORD` values.*

## How to Access Admin Panel

1. **Start the Laravel server:**
   ```bash
   php artisan serve
   ```

2. **Navigate to the admin login page:**
   ```
   http://localhost:8000/admin/login
   ```

3. **Enter the credentials:**
   - Username: `admin`
   - Password: `admin123`

4. **After successful login, you'll be redirected to:**
   ```
   http://localhost:8000/admin/admin_dash
   ```

## Protected Admin Routes
All admin routes are protected by authentication middleware:

- `/admin/admin_dash` - Admin Dashboard
- `/admin/terms` - Terms Management
- `/admin/edit_terms` - Edit Terms
- `/admin/customer` - Customer Management
- `/admin/payment` - Payment Management
- `/admin/contact` - Contact Management
- `/admin/admin_profile` - Admin Profile
- `/admin/edit_profile` - Edit Profile

## Security Features

### 1. Session Management
- Uses Laravel's built-in session handling
- Admin sessions are stored securely
- Automatic session cleanup on logout

### 2. Route Protection
- All admin routes require authentication
- Unauthorized access redirects to login page
- Middleware prevents direct URL access

### 3. Form Security
- CSRF protection enabled
- POST method for login form
- Input validation and sanitization

### 4. Password Security
- Environment-based credential storage
- Password toggle functionality for better UX
- Secure password handling

## File Structure

### Controllers
- `app/Http/Controllers/AdminController.php` - Main admin controller with authentication logic

### Middleware
- `app/Http/Middleware/AdminAuth.php` - Authentication middleware for route protection

### Views
- `resources/views/admin/admin_login.blade.php` - Admin login form

### Routes
- `routes/web.php` - Updated with controller-based routes and middleware

### Configuration
- `.env` - Admin credentials configuration
- `bootstrap/app.php` - Middleware registration

## Customization

### Changing Admin Credentials
Update the `.env` file:
```env
ADMIN_USERNAME=your_new_username
ADMIN_PASSWORD=your_new_password
```

### Adding More Admin Users
You can extend the system to support multiple admin users by:
1. Creating an `admins` table
2. Updating the authentication logic in `AdminController`
3. Using database-based authentication instead of environment variables

### Styling Customization
The login page uses:
- Bootstrap 5.3.0 for responsive design
- Font Awesome 6.4.0 for icons
- Custom CSS with NGO-themed colors (saffron and blue)
- Hindi language support

## Troubleshooting

### Common Issues

1. **"Route not found" error:**
   - Make sure you've cleared the route cache: `php artisan route:clear`

2. **"Class not found" error:**
   - Run: `composer dump-autoload`

3. **Session not working:**
   - Check if session driver is properly configured in `.env`
   - Run: `php artisan config:clear`

4. **Middleware not working:**
   - Ensure middleware is registered in `bootstrap/app.php`
   - Clear config cache: `php artisan config:clear`

## Next Steps

1. **Test the login functionality** with the provided credentials
2. **Customize the admin dashboard** as needed
3. **Add more admin features** like user management, content management, etc.
4. **Implement database-based admin authentication** for multiple admin users
5. **Add password reset functionality** if needed

## Support
For any issues or questions regarding the admin login system, please refer to the Laravel documentation or contact the development team.
