// --- Donation Submit ---
document.getElementById("submitBtn").addEventListener("click", function() {
    const form = document.getElementById("donationForm");

    const name = document.getElementById('donorName').value.trim();
    const email = document.getElementById('donorEmail').value.trim();
    const mobile = document.getElementById('donorPhone').value.trim();
    const state = form.state.value;
    const city = form.city.value.trim();
    const address = form.address.value.trim();
    const amount = document.getElementById('amount').value.trim();
    const purpose = document.getElementById('donationPurpose').options[document.getElementById('donationPurpose').selectedIndex].text;

    if (!amount) {
        alert("कृपया पहले राशि चुनें या दर्ज करें।");
        return;
    }

    if (!name || !email || !mobile || !state || !city || !address) {
        alert("कृपया सभी आवश्यक जानकारी भरें।");
        return;
    }

    alert(`धन्यवाद, ${name} जी!\nआपने ₹${amount} ("${purpose}") दान करने के लिए चुना है।`);

    document.getElementById('thankYouTitle').innerText = `धन्यवाद, ${name} जी!`;
    document.getElementById('thankYouMessage').innerText = `आपने ₹${amount} ("${purpose}") दान करने के लिए चुना है। कृपया नीचे दिए गए QR कोड को स्कैन करके पेमेंट करें:`;
    document.getElementById("thankYouModal").style.display = "flex";

    const formData = new FormData(form);
    fetch("", {
        method: "POST",
        body: formData
    }).then(res => res.text()).then(data => {
        console.log("Form data submitted to PHP.");
    });
});

// --- Modal Close ---
document.querySelector(".modal .close").addEventListener("click", function() {
    document.getElementById("thankYouModal").style.display = "none";
    document.getElementById("donationForm").reset();
    document.getElementById("displayAmount").innerText = "";
});

// --- Tier Selection ---
document.querySelectorAll('.select-tier-btn').forEach(function(btn) {
    btn.addEventListener('click', function(e) {
        e.preventDefault();
        const amount = this.getAttribute('data-amount');
        const type = this.getAttribute('data-type');

        document.getElementById('amount').value = amount;
        document.getElementById('donationType').value = type;
        document.getElementById('displayAmount').innerText = "₹" + amount;
    });
});

document.getElementById('selectCustom').addEventListener('click', function(e) {
    e.preventDefault();
    let customAmt = document.getElementById('customAmount').value;

    if (customAmt > 0) {
        document.getElementById('amount').value = customAmt;
        document.getElementById('donationType').value = "अन्य राशि";
        document.getElementById('displayAmount').innerText = "₹" + customAmt;
    } else {
        alert("कृपया मान्य राशि दर्ज करें।");
    }
});

// --- Tier Button Highlight ---
const tierButtons = document.querySelectorAll('.select-tier-btn');

tierButtons.forEach(btn => {
    btn.addEventListener('click', function() {
        document.querySelectorAll('.donation-tier').forEach(tier => {
            tier.classList.remove('active-tier');
            const tierBtn = tier.querySelector('.select-tier-btn');
            if (tierBtn) {
                tierBtn.classList.remove('active');
                tierBtn.textContent = 'चुनें';
            }
        });

        const selectedTier = this.closest('.donation-tier');
        selectedTier.classList.add('active-tier');
        this.classList.add('active');
        this.textContent = 'चयनित';

        const amount = this.getAttribute('data-amount');
        const type = this.getAttribute('data-type');

        document.getElementById('amount').value = amount.replace(/,/g, '');
        document.getElementById('donationType').value = type;
        document.getElementById('displayAmount').textContent = '₹' + amount;
    });
});

document.getElementById('selectCustom').addEventListener('click', function() {
    const customAmount = document.getElementById('customAmount').value;
    if (customAmount && parseInt(customAmount) > 0) {
        document.querySelectorAll('.donation-tier').forEach(tier => {
            tier.classList.remove('active-tier');
            const tierBtn = tier.querySelector('.select-tier-btn');
            if (tierBtn) {
                tierBtn.classList.remove('active');
                tierBtn.textContent = 'चुनें';
            }
        });

        document.getElementById('amount').value = customAmount;
        document.getElementById('donationType').value = 'अन्य राशि';
        document.getElementById('displayAmount').textContent = '₹' + customAmount;
    }
});

// --- Gallery Filter ---
const buttons = document.querySelectorAll('.filter-button');
const items = document.querySelectorAll('.gallery-item');

buttons.forEach(btn => {
    btn.addEventListener('click', () => {
        buttons.forEach(b => b.classList.remove('active'));
        btn.classList.add('active');

        const filter = btn.getAttribute('data-filter');

        items.forEach(item => {
            if (filter === 'all' || item.classList.contains(filter)) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    });
});

// --- Image Modal ---
function openModal(wrapper) {
    const img = wrapper.querySelector("img");

    document.getElementById("modalImage").src = img.src;
    document.getElementById("imgModal").style.display = "block";
}

function closeModal() {
    document.getElementById("imgModal").style.display = "none";
}

// --- Contact Form ---
document.getElementById('contactForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const form = this;
    const formData = new FormData(form);

    fetch('contact_form.php', {
        method: 'POST',
        body: formData
    })
    .then(res => res.text())
    .then(result => {
        console.log('Response:', result);
        if (result.trim() === 'success') {
            alert('✅ आपका संदेश सफलतापूर्वक भेजा गया है।');
            form.reset();
        } else {
            alert(result);
        }
    })
    .catch(err => {
        console.error(err);
        alert('❌ नेटवर्क त्रुटि।');
    });
});
