# MySQL Database Authentication System - <PERSON><PERSON><PERSON> Narma<PERSON>hwar <PERSON><PERSON>

## Overview
The admin authentication system has been upgraded to use MySQL database instead of environment variables. This provides better security, scalability, and user management capabilities.

## Database Configuration

### MySQL Connection Settings
The `.env` file has been updated with MySQL configuration:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ngo_database
DB_USERNAME=root
DB_PASSWORD=
```

### Database Tables

#### Admins Table Structure
```sql
CREATE TABLE `admins` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL UNIQUE,
  `email` varchar(255) NOT NULL UNIQUE,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
  `status` enum('active','inactive') DEFAULT 'active',
  `last_login_at` timestamp NULL DEFAULT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
);
```

## Default Admin Accounts

### Super Administrator
- **Username:** `admin`
- **Email:** `<EMAIL>`
- **Password:** `admin123`
- **Role:** `super_admin`
- **Phone:** `+91-**********`

### Moderator
- **Username:** `moderator`
- **Email:** `<EMAIL>`
- **Password:** `mod123`
- **Role:** `moderator`
- **Phone:** `+91-**********`

### NGO Administrator
- **Username:** `ngo_admin`
- **Email:** `<EMAIL>`
- **Password:** `ngo123`
- **Role:** `admin`
- **Phone:** `+91-**********`

## Features

### Authentication Features
- ✅ Database-based user authentication
- ✅ Multiple admin roles (super_admin, admin, moderator)
- ✅ User status management (active/inactive)
- ✅ Login with username or email
- ✅ Password hashing with Laravel's Hash facade
- ✅ Last login timestamp tracking
- ✅ Session-based authentication
- ✅ Role-based permissions

### Security Features
- ✅ Encrypted password storage
- ✅ CSRF protection
- ✅ Session management
- ✅ Input validation
- ✅ SQL injection protection
- ✅ XSS protection

## Admin Model Features

### Methods Available
```php
// Check if admin is active
$admin->isActive()

// Check if admin is super admin
$admin->isSuperAdmin()

// Check if admin is admin or super admin
$admin->isAdmin()

// Update last login timestamp
$admin->updateLastLogin()
```

### Scopes Available
```php
// Get only active admins
Admin::active()->get()

// Get admins by role
Admin::byRole('super_admin')->get()
```

## Setup Instructions

### 1. Database Setup
```bash
# Create MySQL database (if not exists)
mysql -u root -p -e "CREATE DATABASE ngo_database;"

# Run migrations
php artisan migrate

# Seed admin users
php artisan db:seed --class=AdminSeeder
```

### 2. Configuration
Ensure your `.env` file has the correct MySQL settings:
```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=ngo_database
DB_USERNAME=root
DB_PASSWORD=your_mysql_password
```

### 3. Clear Cache
```bash
php artisan config:clear
php artisan cache:clear
```

## Usage

### Login Process
1. Navigate to: `http://localhost:8000/admin/login`
2. Enter username/email and password
3. System validates against database
4. Creates secure session on successful login
5. Redirects to admin dashboard

### Adding New Admin Users
```php
use App\Models\Admin;
use Illuminate\Support\Facades\Hash;

Admin::create([
    'name' => 'New Admin',
    'username' => 'newadmin',
    'email' => '<EMAIL>',
    'password' => Hash::make('password123'),
    'phone' => '+91-1234567890',
    'role' => 'admin',
    'status' => 'active',
]);
```

### Role Management
- **super_admin**: Full access to all features
- **admin**: Standard admin access
- **moderator**: Limited access for content moderation

## API Endpoints

### Authentication Routes
- `GET /admin/login` - Show login form
- `POST /admin/login` - Process login
- `GET /admin/logout` - Logout admin

### Protected Routes (require authentication)
- `GET /admin/admin_dash` - Admin dashboard
- `GET /admin/terms` - Terms management
- `GET /admin/customer` - Customer management
- `GET /admin/payment` - Payment management
- `GET /admin/contact` - Contact management
- `GET /admin/admin_profile` - Admin profile
- `GET /admin/edit_profile` - Edit profile

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   ```bash
   # Check MySQL service is running
   # Verify credentials in .env file
   php artisan config:clear
   ```

2. **Migration Errors**
   ```bash
   # Reset migrations if needed
   php artisan migrate:reset
   php artisan migrate
   ```

3. **Seeder Issues**
   ```bash
   # Re-run seeder
   php artisan db:seed --class=AdminSeeder --force
   ```

4. **Login Not Working**
   - Verify admin exists in database
   - Check password is correctly hashed
   - Ensure admin status is 'active'

### Database Queries for Debugging
```sql
-- Check all admin users
SELECT * FROM admins;

-- Check specific admin
SELECT * FROM admins WHERE username = 'admin';

-- Update admin password manually
UPDATE admins SET password = '$2y$12$...' WHERE username = 'admin';
```

## Security Best Practices

1. **Change Default Passwords** immediately after setup
2. **Use Strong Passwords** for all admin accounts
3. **Regular Password Updates** for security
4. **Monitor Login Activity** using last_login_at field
5. **Deactivate Unused Accounts** by setting status to 'inactive'
6. **Regular Database Backups** for data protection

## Next Steps

1. **Test all login credentials** provided above
2. **Change default passwords** for security
3. **Add more admin users** as needed
4. **Implement password reset** functionality
5. **Add email verification** for new admins
6. **Create admin management interface** for user management

The MySQL database authentication system is now fully functional and ready for production use!
