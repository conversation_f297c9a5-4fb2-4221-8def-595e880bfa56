<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use Illuminate\Support\Facades\Session;

class AdminController extends Controller
{
    /**
     * Show the admin login form
     */
    public function showLoginForm()
    {
        // If admin is already logged in, redirect to dashboard
        if (Session::has('admin_logged_in')) {
            return redirect()->route('admin.dashboard');
        }
        
        return view('admin.admin_login');
    }

    /**
     * Handle admin login
     */
    public function login(Request $request)
    {
        $request->validate([
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        // For this NGO application, we'll use a simple admin authentication
        // You can modify this to use database authentication later
        $adminUsername = env('ADMIN_USERNAME', 'admin');
        $adminPassword = env('ADMIN_PASSWORD', 'admin123');

        if ($request->username === $adminUsername && $request->password === $adminPassword) {
            // Set admin session
            Session::put('admin_logged_in', true);
            Session::put('admin_username', $request->username);
            
            return redirect()->route('admin.dashboard')->with('success', 'स्वागत है! आप सफलतापूर्वक लॉग इन हो गए हैं।');
        }

        return back()->withErrors([
            'login' => 'गलत उपयोगकर्ता नाम या पासवर्ड। कृपया पुनः प्रयास करें।',
        ])->withInput($request->only('username'));
    }

    /**
     * Show admin dashboard
     */
    public function dashboard()
    {
        if (!Session::has('admin_logged_in')) {
            return redirect()->route('admin.login')->with('error', 'कृपया पहले लॉग इन करें।');
        }
        
        return view('admin.admin_dash');
    }

    /**
     * Handle admin logout
     */
    public function logout()
    {
        Session::forget('admin_logged_in');
        Session::forget('admin_username');
        Session::flush();
        
        return redirect()->route('admin.login')->with('success', 'आप सफलतापूर्वक लॉग आउट हो गए हैं।');
    }

    /**
     * Show admin terms page
     */
    public function terms()
    {
        if (!Session::has('admin_logged_in')) {
            return redirect()->route('admin.login');
        }
        return view('admin.terms');
    }

    /**
     * Show edit terms page
     */
    public function editTerms()
    {
        if (!Session::has('admin_logged_in')) {
            return redirect()->route('admin.login');
        }
        return view('admin.edit_terms');
    }

    /**
     * Show customers page
     */
    public function customers()
    {
        if (!Session::has('admin_logged_in')) {
            return redirect()->route('admin.login');
        }
        return view('admin.customer');
    }

    /**
     * Show payments page
     */
    public function payments()
    {
        if (!Session::has('admin_logged_in')) {
            return redirect()->route('admin.login');
        }
        return view('admin.payment');
    }

    /**
     * Show contacts page
     */
    public function contacts()
    {
        if (!Session::has('admin_logged_in')) {
            return redirect()->route('admin.login');
        }
        return view('admin.contact');
    }

    /**
     * Show admin profile page
     */
    public function profile()
    {
        if (!Session::has('admin_logged_in')) {
            return redirect()->route('admin.login');
        }
        return view('admin.admin_profile');
    }

    /**
     * Show edit profile page
     */
    public function editProfile()
    {
        if (!Session::has('admin_logged_in')) {
            return redirect()->route('admin.login');
        }
        return view('admin.edit_profile');
    }
}
