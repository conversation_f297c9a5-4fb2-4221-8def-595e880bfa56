<?php

use Illuminate\Support\Facades\Route;

// Route::get('/', function () {
//     return view('welcome');
// });

// for web
Route::view('/','welcome');
Route::view('privacy','privacy');
Route::view('terms','terms');
Route::view('gallery','gallery');


// for admin
Route::view('/admin/admin_dash','admin.admin_dash');
Route::view('/admin/terms','admin.terms');
Route::view('/admin/edit_terms','admin.edit_terms');
Route::view('/admin/customer','admin.customer');
Route::view('/admin/payment','admin.payment');
Route::view('/admin/contact','admin.contact');
Route::view('/admin/admin_profile','admin.admin_profile');
Route::view('/admin/edit_profile','admin.edit_profile');
Route::view('/admin/admin_login','admin.admin_login');
Route::view('/admin/logout','admin.logout');