<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AdminController;

// Route::get('/', function () {
//     return view('welcome');
// });

// for web
Route::view('/','welcome');
Route::view('privacy','privacy');
Route::view('terms','terms');
Route::view('gallery','gallery');

// Admin Authentication Routes (no middleware needed)
Route::get('/admin/login', [AdminController::class, 'showLoginForm'])->name('admin.login');
Route::post('/admin/login', [AdminController::class, 'login']);
Route::get('/admin/logout', [AdminController::class, 'logout'])->name('admin.logout');

// Protected Admin Routes (require authentication)
Route::middleware(['admin.auth'])->group(function () {
    Route::get('/admin/admin_dash', [AdminController::class, 'dashboard'])->name('admin.dashboard');
    Route::get('/admin/terms', [AdminController::class, 'terms'])->name('admin.terms');
    Route::get('/admin/edit_terms', [AdminController::class, 'editTerms'])->name('admin.edit_terms');
    Route::get('/admin/customer', [AdminController::class, 'customers'])->name('admin.customers');
    Route::get('/admin/payment', [AdminController::class, 'payments'])->name('admin.payments');
    Route::get('/admin/contact', [AdminController::class, 'contacts'])->name('admin.contacts');
    Route::get('/admin/admin_profile', [AdminController::class, 'profile'])->name('admin.profile');
    Route::get('/admin/edit_profile', [AdminController::class, 'editProfile'])->name('admin.edit_profile');
});

// Redirect /admin to login page
Route::redirect('/admin', '/admin/login');