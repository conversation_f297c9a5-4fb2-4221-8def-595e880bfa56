<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Login</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />

    <style>
        :root {
            --saffron: #FF9933;
            --saffron-dark: #E67E22;
            --shiva-blue: #3a5a78;
            --shiva-blue-dark: #2c3e50;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .login-container {
            max-width: 450px;
            margin: 5rem auto;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .login-header {
            background: linear-gradient(135deg, var(--shiva-blue), var(--shiva-blue-dark));
            color: white;
            padding: 1.5rem;
            text-align: center;
        }

        .login-header img {
            width: 70px;
            margin-bottom: 1rem;
        }

        .login-body {
            background: white;
            padding: 2rem;
        }

        .form-control {
            padding: 12px 15px;
            border-radius: 5px;
        }

        .btn-login {
            background-color: var(--saffron);
            border: none;
            padding: 12px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-login:hover {
            background-color: var(--saffron-dark);
        }

        .input-group-text {
            background-color: #e9ecef;
            border-right: none;
        }

        .form-floating label {
            padding: 1rem 1.75rem;
        }

        .form-floating>.form-control:focus~label {
            color: var(--shiva-blue);
        }

        .password-toggle {
            cursor: pointer;
            background-color: #e9ecef;
            border-left: none;
        }

        .forgot-link {
            color: var(--shiva-blue);
            text-decoration: none;
        }

        .forgot-link:hover {
            text-decoration: underline;
        }

        .admin-icon {
            font-size: 1.2rem;
            margin-right: 8px;
        }
    </style>


</head>

<body>
    <div class="container">
        <div class="login-container">
            <div class="login-header">
                <img src="{{ asset('admin/images/om.png') }}" alt="Shiva Icon">
                <h3><i class="fas fa-user-shield admin-icon"></i> संस्थान प्रशासन</h3>
                <p class="mb-0">Rudra Narmadeshwar Seva Samiti Admin Panel</p>
            </div>
            <div class="login-body">
                <!-- Success Message Alert -->
                @if(session('success'))
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    {{ session('success') }}
                </div>
                @endif

                <!-- Error Message Alert -->
                @if($errors->any())
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    @foreach($errors->all() as $error)
                        {{ $error }}
                    @endforeach
                </div>
                @endif

                @if(session('error'))
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    {{ session('error') }}
                </div>
                @endif

                <form id="adminLoginForm" method="POST" action="{{ route('admin.login') }}" autocomplete="off">
                    @csrf
                    <div class="form-floating mb-3">
                        <input type="text" name="username" class="form-control" id="username" placeholder="Username or Email" required>
                        <label for="username"><i class="fas fa-user me-2"></i> उपयोगकर्ता नाम या ईमेल</label>
                    </div>


                    <div class="form-floating mb-3">
                        <div class="input-group">
                            <input type="password" name="password" class="form-control" id="password" placeholder="Password" required>
                            <span class="input-group-text password-toggle" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </span>
                            <label for="password"><i class="fas fa-key me-2"></i> पासवर्ड</label>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">मुझे याद रखें</label>
                        </div>
                        <a href="#" class="forgot-link">पासवर्ड भूल गए?</a>
                    </div>
                    <button type="submit" name="login" class="btn btn-login btn-primary w-100">
                        <i class="fas fa-sign-in-alt me-2"></i> लॉग इन
                    </button>
                </form>
            </div>

            <div class="login-footer text-center p-3 bg-light">
                <p class="small mb-0">© 2023 रूद्र नर्मदेश्वर सेवा समिति. सर्वाधिकार सुरक्षित</p>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Password toggle functionality
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const toggleIcon = this.querySelector('i');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        });

        // Form validation
        document.getElementById('adminLoginForm').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();

            if (!username || !password) {
                e.preventDefault();
                alert('कृपया उपयोगकर्ता नाम और पासवर्ड दोनों भरें।');
                return false;
            }
        });

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                if (alert.classList.contains('alert-success') || alert.classList.contains('alert-danger')) {
                    alert.style.transition = 'opacity 0.5s';
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.remove();
                    }, 500);
                }
            });
        }, 5000);
    </script>
</body>

</html>