<?php echo $__env->make('admin.includes.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<!DOCTYPE html>
<html lang="hi">

<head>
    <meta charset="UTF-8">
    <title>नियमावली संपादन</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    
    <!-- CKEditor -->
    <script src="https://cdn.ckeditor.com/4.22.1/standard/ckeditor.js"></script>
    <!-- Custom Styling -->
    <style>
        body {
            background-color: #f1f4f9;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        h2 {
            color: #2c3e50;
        }
        .btn-success {
            padding: 10px 30px;
            font-size: 18px;
        }
        .alert {
            font-size: 16px;
        }
        textarea {
            resize: none;
        }
    </style>
</head>

<body>
    <div class="container my-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card p-4">
                    <h2 class="text-center mb-4">✏️ नियमावली संपादन</h2>

                    <form method="GET">
                        <textarea name="content" id="content" rows="15"></textarea>
                        <script>
                            CKEDITOR.replace('content');
                        </script>

                        <div class="text-center mt-4">
                            <button type="submit" name="update" class="btn btn-success">💾 अपडेट करें</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
<?php echo $__env->make('admin.includes.footer', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php /**PATH C:\Users\<USER>\Documents\augment-projects\ngo\ngo\resources\views/admin/edit_terms.blade.php ENDPATH**/ ?>