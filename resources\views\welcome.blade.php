@includeif('web.header')

<style>
    /* Donation */

    .active-tier {
        border: 2px solid orange;
        background-color: #fff7f0;
        border-radius: 8px;
    }

    .select-tier-btn.active {
        background-color: orange !important;
        color: white !important;
        border: none;
    }
</style>
<!-- Hero Section -->
<section class="hero-section">
    <div class="container text-center animate" style="animation-delay: 0.2s;">
        <div class="shiva-icon">
            <i class="fas fa-om"></i>
        </div>
        <h1 class="display-4 fw-bold mb-4 hindi">सवा लाख शिवलिंग प्रतिष्ठान महायज्ञ</h1>
        <p class="lead mb-5 hindi">एक दिव्य धाम की स्थापना जहाँ आध्यात्म, सेवा और पर्यावरण संरक्षण का होगा समन्वय</p>
        <a href="#donate" class="btn btn-accent btn-lg px-4 me-2 hindi">सहयोग करें</a>
        <a href="#mission" class="btn btn-outline-light btn-lg px-4 hindi">और जानें</a>
    </div>
</section>

<!-- Mission Section -->
<section id="mission" class="py-5">
    <div class="container">
        <div class="text-center mb-5">
            <h2 class="section-heading hindi">हमारा उद्देश्य</h2>
            <p class="lead hindi">सेवा परमो धर्म: - सेवा ही सर्वोच्च धर्म है</p>
        </div>
        <div class="row g-4">
            <div class="col-md-6">
                <div class="mission-card animate" style="animation-delay: 0.2s;">
                    <h3 class="hindi">आध्यात्मिक केंद्र</h3>
                    <p class="hindi">एक ऐसे दिव्य धाम की स्थापना जो न केवल श्रद्धालुओं के लिए पूजा एवं साधना का पावन केंद्र हो,
                        अपितु सम्पूर्ण मानव समाज के लिए आध्यात्मिक जागरण का स्रोत बने।</p>
                    <div class="text-center mt-4">
                        <i class="fas fa-hands-praying fa-3x text-muted"></i>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mission-card animate" style="animation-delay: 0.4s;">
                    <h3 class="hindi">सेवा एवं संरक्षण</h3>
                    <p class="hindi">समाज सेवा, गौ संरक्षण, वृक्षारोपण और पर्यावरण संरक्षण के माध्यम से मानव कल्याण और प्रकृति
                        संरक्षण की दिशा में कार्य करना।</p>
                    <div class="text-center mt-4">
                        <i class="fas fa-hand-holding-heart fa-3x text-muted"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Project Section -->
<section id="project" class="py-5 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <h2 class="section-heading hindi">सवा लाख शिवलिंग प्रतिष्ठान</h2>
                <p class="hindi">इस पवित्र धाम में सवा लाख (1,25,000) शिवलिंगों की प्रतिष्ठा की जाएगी, जो भगवान शिव की अनंत
                    महिमा, चेतना एवं सार्वभौमिक एकता का प्रतीक हैं।</p>

                <div class="row mt-4">
                    <div class="col-6 col-md-4 text-center mb-4">
                        <div class="linga-count hindi">1,25,000</div>
                        <div class="hindi">शिवलिंग</div>
                    </div>
                    <div class="col-6 col-md-4 text-center mb-4">
                        <div class="linga-count">5</div>
                        <div class="hindi">वर्षीय परियोजना</div>
                    </div>
                    <div class="col-6 col-md-4 text-center mb-4">
                        <div class="linga-count">11</div>
                        <div class="hindi">एकड़ भूमि</div>
                    </div>
                </div>

                <p class="hindi">यह परियोजना केवल धार्मिक महत्व की नहीं है, अपितु यह एक समग्र मानव कल्याण की भावना से प्रेरित
                    प्रयास है, जिसका उद्देश्य जीवन के विभिन्न पक्षों — अध्यात्म, पर्यावरण, संस्कृति और सेवा — के मध्य संतुलन
                    स्थापित करना है।</p>
            </div>
            <div class="col-lg-6">
                <div class="ratio ratio-16x9 shadow-lg rounded overflow-hidden animate" style="animation-delay: 0.4s;">
                    <img src="{{ asset('asset/images/ss.jpg') }}" alt="Shiva Temple" class="img-fluid">
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-md-4 mb-4">
                <div class="mission-card h-100">
                    <h4 class="hindi">आध्यात्मिक विकास</h4>
                    <p class="hindi">यह धाम आत्मिक शांति, ध्यान, योग एवं सदाचार की अनुभूति का केंद्र होगा।</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="mission-card h-100">
                    <h4 class="hindi">सांस्कृतिक संरक्षण</h4>
                    <p class="hindi">हमारी प्राचीन संस्कृति एवं परंपराओं का संरक्षण एवं प्रचार-प्रसार।</p>
                </div>
            </div>
            <div class="col-md-4 mb-4">
                <div class="mission-card h-100">
                    <h4 class="hindi">पर्यावरण संरक्षण</h4>
                    <p class="hindi">वृक्षारोपण, जल-संरक्षण एवं प्राकृतिक जीवन शैली को बढ़ावा।</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Divine Mission Section -->
<section id="divine-mission" class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <div class="shiva-icon mb-3">
                <i class="fas fa-om fa-3x"></i>
            </div>
            <h2 class="section-heading hindi">पावन धाम निर्माण महायज्ञ</h2>
            <p class="lead hindi">1,25,000 शिवलिंगों के माध्यम से ब्रह्मांडीय चेतना का साकार स्वरूप</p>
        </div>

        <!-- Mission Highlights -->
        <div class="row g-4 mb-5">
            <div class="col-md-4">
                <div class="mission-highlight-card text-center p-4 h-100">
                    <div class="icon-circle mb-3">
                        <i class="fas fa-hands-praying fa-2x"></i>
                    </div>
                    <h4 class="hindi">पंचमुखी धाम</h4>
                    <p class="hindi">भक्ति, ज्ञान, सेवा, संस्कृति और पर्यावरण का अद्भुत संगम</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mission-highlight-card text-center p-4 h-100">
                    <div class="icon-circle mb-3">
                        <i class="fas fa-atom fa-2x"></i>
                    </div>
                    <h4 class="hindi">ब्रह्मांडीय ऊर्जा केंद्र</h4>
                    <p class="hindi">सवा लाख शिवलिंगों से निर्मित दिव्य शक्ति पीठ</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mission-highlight-card text-center p-4 h-100">
                    <div class="icon-circle mb-3">
                        <i class="fas fa-heartbeat fa-2x"></i>
                    </div>
                    <h4 class="hindi">सामाजिक सामरस्या</h4>
                    <p class="hindi">विभिन्नताओं में एकता का जीवंत प्रतीक</p>
                </div>
            </div>
        </div>

        <!-- Sacred Installation Diagram -->
        <div class="row align-items-center">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <div class="sacred-structure text-center">
                    <div class="lingam-grid mb-4">
                        <!-- This would be replaced with an actual SVG graphic -->
                        <i class="fas fa-dharmachakra fa-5x text-ornate"></i>
                    </div>
                    <h4 class="hindi">वैदिक मंडल रचना</h4>
                    <p class="hindi">1.25 लाख शिवलिंगों की वैज्ञानिक वास्तु व्यवस्था</p>
                </div>
            </div>
            <div class="col-lg-6">
                <div class="mission-details">
                    <div class="d-flex mb-4">
                        <div class="flex-shrink-0 me-3">
                            <span class="badge rounded-circle bg-primary p-3">1</span>
                        </div>
                        <div>
                            <h5 class="hindi">दिव्य संरचना</h5>
                            <p class="hindi">वैदिक गणित के अनुरूप 125 गुणा 1000 शिवलिंगों की पवित्र व्यवस्था</p>
                        </div>
                    </div>
                    <div class="d-flex mb-4">
                        <div class="flex-shrink-0 me-3">
                            <span class="badge rounded-circle bg-primary p-3">2</span>
                        </div>
                        <div>
                            <h5 class="hindi">पाँच तत्वों का संतुलन</h5>
                            <p class="hindi">पृथ्वी, जल, अग्नि, वायु और आकाश के साथ पारिस्थितिक संरक्षण</p>
                        </div>
                    </div>
                    <div class="d-flex">
                        <div class="flex-shrink-0 me-3">
                            <span class="badge rounded-circle bg-primary p-3">3</span>
                        </div>
                        <div>
                            <h5 class="hindi">सतत विकास</h5>
                            <p class="hindi">जैविक खेती, जल संचयन और सौर ऊर्जा का एकीकरण</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Call to Action -->
        <div class="mission-cta text-center mt-5 p-4 rounded">
            <h3 class="hindi mb-3">इस पुण्य यज्ञ में अपनी आहुति दें</h3>
            <a href="#donate" class="btn btn-accent btn-lg mx-2 mb-4 hindi">शिवलिंग प्रतिष्ठा में सहयोग</a>
            <a href="#contact" class="btn btn-outline-primary btn-lg mx-2 mb-4 hindi">स्वयंसेवक बनें</a>
        </div>
    </div>
</section>


<!-- Divine Donation Section -->
<section id="donate" class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <div class="donation-icon mb-3">
                <img src="{{ asset('asset/images/om.png') }}" alt="Donation" width="80">
            </div>
            <h2 class="section-heading hindi">दान महायज्ञ</h2>
            <p class="lead hindi">सवा लाख शिवलिंग प्रतिष्ठा में अपना पुण्य योगदान दें</p>
        </div>

        <div class="row g-4">
            <!-- Donation Options -->
            <div class="col-lg-6">
                <div class="donation-options-card p-4 h-100">
                    <h4 class="text-center mb-4 hindi">शिवलिंग प्रतिष्ठा सहयोग</h4>
                    <form action="" method="post" id="donationForm">
                        <input type="hidden" name="amount" id="amount" value="">
                        <input type="hidden" name="donation_type" id="donationType" value="">
                        <div class="row g-3">
                            <!-- Donation Tier 1 -->
                            <div class="col-md-6">
                                <div class="donation-tier text-center p-3" data-amount="5,000">
                                    <div class="tier-header">
                                        <i class="fas fa-om tier-icon"></i>
                                        <h5 class="hindi mt-2">एक शिवलिंग</h5>
                                    </div>
                                    <div class="tier-amount">₹5,000</div>
                                    <p class="small hindi">पूर्ण अनुष्ठान सहित</p>
                                    <!-- <button type="button" class="select-tier-btn" data-amount="5,000" data-type="एक शिवलिंग">चुनें</button> -->
                                    <button type="button" class="btn btn-sm btn-outline-accent select-tier-btn hindi" data-amount="5,000" data-type="एक शिवलिंग">चुनें</button>
                                    <!-- <button class="btn btn-sm btn-outline-accent select-tier hindi">चुनें</button> -->
                                </div>
                            </div>

                            <!-- Donation Tier 2 -->
                            <div class="col-md-6">
                                <div class="donation-tier text-center p-3" data-amount="51,000">
                                    <div class="tier-header">
                                        <i class="fas fa-om tier-icon"></i>
                                        <i class="fas fa-om tier-icon"></i>
                                        <h5 class="hindi mt-2">एकादश शिवलिंग</h5>
                                    </div>
                                    <div class="tier-amount">₹51,000</div>
                                    <p class="small hindi">विशेष पूजा अधिकार</p>
                                    <button type="button" class="btn btn-sm btn-outline-accent select-tier-btn hindi" data-amount="51,000" data-type="एकादश शिवलिंग">चुनें</button>
                                </div>
                            </div>

                            <!-- Donation Tier 3 -->
                            <div class="col-md-6">
                                <div class="donation-tier active-tier text-center p-3" data-amount="300,000">
                                    <div class="tier-header">
                                        <span class="tier-badge hindi">लोकप्रिय</span>
                                        <i class="fas fa-om tier-icon"></i>
                                        <i class="fas fa-om tier-icon"></i>
                                        <i class="fas fa-om tier-icon"></i>
                                        <h5 class="hindi mt-2">एकसठ शिवलिंग</h5>
                                    </div>
                                    <div class="tier-amount">₹300,000</div>
                                    <p class="small hindi">नामांकन + प्रमाणपत्र</p>
                                    <button type="button" class="btn btn-sm btn-accent select-tier-btn hindi" data-amount="300,000" data-type="एकसठ शिवलिंग">चयनित</button>
                                </div>
                            </div>

                            <!-- Donation Tier 4 -->
                            <div class="col-md-6">
                                <div class="donation-tier text-center p-3" data-amount="5,40,000">
                                    <div class="tier-header">
                                        <i class="fas fa-om tier-icon"></i>
                                        <i class="fas fa-om tier-icon"></i>
                                        <i class="fas fa-om tier-icon"></i>
                                        <i class="fas fa-om tier-icon"></i>
                                        <h5 class="hindi mt-2">अष्टोत्तर शत शिवलिंग</h5>
                                    </div>
                                    <div class="tier-amount">₹5,40,000</div>
                                    <p class="small hindi">विशेष स्मारिका + यात्रा</p>
                                    <button class="btn btn-sm btn-outline-accent select-tier-btn hindi" data-amount="5,40,000" data-type="अष्टोत्तर शत शिवलिंग">चुनें</button>
                                </div>
                            </div>

                            <!-- Custom Amount -->
                            <div class="col-12">
                                <div class="custom-amount p-3">
                                    <h5 class="hindi text-center mb-3">अन्य राशि (₹)</h5>
                                    <div class="input-group">
                                        <span class="input-group-text hindi">₹</span>
                                        <input type="number" class="form-control" id="customAmount" placeholder="अपनी इच्छानुसार राशि">
                                        <button class="btn btn-accent hindi text-white" id="selectCustom">चुनें</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>
            </div>

            <!-- Donation Form -->
            <div class="col-lg-6">
                <div class="donation-form-card p-4">
                    <div class="form-header text-center mb-4">
                        <h4 class="hindi">दान प्रपत्र</h4>
                        <div class="selected-amount-display">
                            <span class="hindi">चयनित राशि:</span>
                            <span id="displayAmount">₹300,000</span>
                        </div>
                    </div>


                    <div class="mb-3">
                        <label for="donorName" class="form-label hindi">पूरा नाम *</label>
                        <input type="text" name="name" class="form-control" id="donorName" required>
                    </div>

                    <div class="row g-3 mb-3">
                        <div class="col-md-6">
                            <label for="donorEmail" class="form-label hindi">ईमेल *</label>
                            <input type="email" name="email" class="form-control" id="donorEmail" required>
                        </div>
                        <div class="col-md-6">
                            <label for="donorPhone" class="form-label hindi">मोबाइल नंबर *</label>
                            <input type="tel" name="mobile" class="form-control" id="donorPhone" required>
                        </div>
                    </div>

                    <div class="row g-3 mb-3">
                        <div class="col-md-6">
                            <label for="state" class="form-label hindi">राज्य(state) *</label>
                            <select class="form-select" name="state">
                                <option selected disabled value="Not Selected">राज्य चुनें</option>
                                <option value="Chhattisgarh">छत्तीसगढ</option>
                                <option value="Gujarat">गुजरात</option>
                                <option value="Uttarakhand">मध्य प्रदेश</option>
                                <option value="Rajasthan">उत्तर प्रदेश</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="donorPhone" class="form-label hindi">शहर(city) *</label>
                            <input type="text" name="city" class="form-control" id="" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label hindi">पता *</label>
                        <textarea class="form-control" name="address" rows="1"
                            required></textarea>
                    </div>


                    <div class="mb-3">
                        <label for="donationPurpose" class="form-label hindi">दान का उद्देश्य</label>
                        <select class="form-select" id="donationPurpose" name="purpose">
                            <option value="shivling" selected class="hindi">शिवलिंग प्रतिष्ठा</option>
                            <option value="temple" class="hindi">मंदिर निर्माण</option>
                            <option value="annadan" class="hindi">अन्नदान</option>
                            <option value="education" class="hindi">विद्यादान</option>
                        </select>
                    </div>

                    <div class="form-check mb-4">
                        <input class="form-check-input" type="checkbox" id="taxExemption" checked>
                        <label class="form-check-label hindi" for="taxExemption">
                            80G के तहत कर छूट प्राप्त करें
                        </label>
                    </div>
                    <button type="button" id="submitBtn" class="btn btn-accent w-100 py-2 hindi text-white">
                        <i class="fas fa-donate me-2"></i> दान करें
                    </button>
                </div>
            </div>
        </div>

        </form>

        <!-- Donation Benefits -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="donation-benefits p-4">
                    <h4 class="text-center mb-4 hindi">आपके दान के लाभ</h4>
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="benefit-item text-center p-3">
                                <i class="fas fa-certificate benefit-icon"></i>
                                <h5 class="hindi">80G प्रमाणपत्र</h5>
                                <p class="small hindi">टैक्स बचत के लिए</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="benefit-item text-center p-3">
                                <i class="fas fa-praying-hands benefit-icon"></i>
                                <h5 class="hindi">नामांकन पत्र</h5>
                                <p class="small hindi">शिवलिंग प्रतिष्ठा में</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="benefit-item text-center p-3">
                                <i class="fas fa-calendar-alt benefit-icon"></i>
                                <h5 class="hindi">नियमित अपडेट</h5>
                                <p class="small hindi">परियोजना प्रगति की</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Thank You Modal -->
<div id="thankYouModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h2 id="thankYouTitle" class="hindi"></h2>
        <p id="thankYouMessage" class="hindi"></p>
        <!-- <img src="{{ asset('images/om.png') }}" alt="Donation" width="80"> -->
        <!-- <img src="qr.jpg" width="200" alt="QR Code" id="qrCodeImage"> -->
    </div>
</div>


<!-- Donation Campaigns Section -->
<section id="donation-campaigns" class="py-5 bg-white">
    <div class="container">
        <div class="text-center mb-5">
            <div class="campaign-icon mb-3">
                <i class="fas fa-hands-helping fa-3x text-saffron"></i>
            </div>
            <h2 class="section-heading hindi">हमारे वर्तमान अभियान</h2>
            <p class="lead hindi">समाज में सकारात्मक बदलाव लाने में हमारी मदद करें</p>
        </div>

        <div class="row g-4">
            <!-- Campaign 1 - Shivling Installation -->
            <div class="col-md-6 col-lg-4">
                <div class="campaign-card">
                    <div class="campaign-badge hindi">प्राथमिकता</div>

                    <img src="{{ asset('asset/images/shiv.jpeg') }}" class="campaign-img" alt="Shivling Installation">
                    <div class="campaign-body">
                        <h3 class="hindi">सवा लाख शिवलिंग प्रतिष्ठान</h3>
                        <div class="progress mb-3">
                            <div class="progress-bar" role="progressbar" style="width: 65%;" aria-valuenow="65" aria-valuemin="0"
                                aria-valuemax="100"></div>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span class="hindi">एकत्रित: ₹82 लाख</span>
                            <span class="hindi">लक्ष्य: ₹1.25 करोड़</span>
                        </div>
                        <p class="campaign-desc hindi">भव्य शिव धाम निर्माण हेतु 1,25,000 शिवलिंगों की प्राण-प्रतिष्ठा</p>
                        <div class="campaign-features">
                            <span class="badge bg-light text-dark hindi"><i class="fas fa-certificate text-saffron me-1"></i> 80G
                                छूट</span>
                            <span class="badge bg-light text-dark hindi"><i class="fas fa-praying-hands text-saffron me-1"></i>
                                नामांकन</span>
                        </div>
                        <a href="#donate" class="btn btn-accent w-100 mt-3 hindi">सहयोग करें</a>
                    </div>
                </div>
            </div>

            <!-- Campaign 2 - Education -->
            <div class="col-md-6 col-lg-4">
                <div class="campaign-card">
                    <img src="{{ asset('asset/images/gurukul.jpg') }}" class="campaign-img" alt="Education">
                    <div class="campaign-body">
                        <h3 class="hindi">विद्यादान अभियान</h3>
                        <div class="progress mb-3">
                            <div class="progress-bar" role="progressbar" style="width: 40%;" aria-valuenow="40" aria-valuemin="0"
                                aria-valuemax="100"></div>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span class="hindi">एकत्रित: ₹4 लाख</span>
                            <span class="hindi">लक्ष्य: ₹10 लाख</span>
                        </div>
                        <p class="campaign-desc hindi">100 गरीब बच्चों को शिक्षा सामग्री एवं छात्रवृत्ति</p>
                        <div class="campaign-features">
                            <span class="badge bg-light text-dark hindi"><i class="fas fa-rupee-sign text-saffron me-1"></i> प्रति
                                बच्चा ₹1000</span>
                            <span class="badge bg-light text-dark hindi"><i class="fas fa-user-graduate text-saffron me-1"></i>
                                प्रगति रिपोर्ट</span>
                        </div>
                        <a href="#donate" class="btn btn-accent w-100 mt-3 hindi">सहयोग करें</a>

                    </div>
                </div>
            </div>

            <!-- Campaign 3 - Annadan -->
            <div class="col-md-6 col-lg-4">
                <div class="campaign-card">
                    <img src="{{ asset('asset/images/bb.jpg') }}" class="campaign-img" alt="Food Donation">
                    <div class="campaign-body">
                        <h3 class="hindi">अन्नदान सेवा</h3>
                        <div class="progress mb-3">
                            <div class="progress-bar" role="progressbar" style="width: 30%;" aria-valuenow="30" aria-valuemin="0"
                                aria-valuemax="100"></div>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <span class="hindi">एकत्रित: ₹1.5 लाख</span>
                            <span class="hindi">लक्ष्य: ₹5 लाख</span>
                        </div>
                        <p class="campaign-desc hindi">प्रतिमाह 1000 गरीबों को पौष्टिक भोजन</p>
                        <div class="campaign-features">
                            <span class="badge bg-light text-dark hindi"><i class="fas fa-utensils text-saffron me-1"></i> प्रति
                                भोजन ₹50</span>
                            <span class="badge bg-light text-dark hindi"><i class="fas fa-calendar-alt text-saffron me-1"></i> मासिक
                                सहयोग</span>
                        </div>
                        <a href="#donate" class="btn btn-accent w-100 mt-3 hindi">सहयोग करें</a>

                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Donation Types -->
        <div class="other-donations mt-5">
            <h4 class="text-center mb-4 hindi">अन्य दान के अवसर</h4>
            <div class="row g-3">
                <div class="col-6 col-md-3">
                    <div class="quick-donation-option text-center p-3">
                        <i class="fas fa-cow fa-2x mb-2 text-saffron"></i>
                        <h5 class="hindi">गौ दान</h5>
                        <p class="small hindi">₹5,100 से शुरू</p>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="quick-donation-option text-center p-3">
                        <i class="fas fa-tree fa-2x mb-2 text-saffron"></i>
                        <h5 class="hindi">वृक्ष दान</h5>
                        <p class="small hindi">₹501 प्रति वृक्ष</p>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="quick-donation-option text-center p-3">
                        <i class="fas fa-book fa-2x mb-2 text-saffron"></i>
                        <h5 class="hindi">ग्रंथ दान</h5>
                        <p class="small hindi">₹1,100 से शुरू</p>
                    </div>
                </div>
                <div class="col-6 col-md-3">
                    <div class="quick-donation-option text-center p-3">
                        <i class="fas fa-birthday-cake fa-2x mb-2 text-saffron"></i>
                        <h5 class="hindi">विशेष दान</h5>
                        <p class="small hindi">जन्मदिन/विवाह</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>


<!-- Divine Gallery Section -->
<section id="gallery" class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <div class="gallery-icon mb-3">
                <i class="fas fa-camera-retro fa-3x" style="color: var(--saffron);"></i>
            </div>
            <h2 class="section-heading hindi">हमारी गतिविधियाँ</h2>
            <p class="lead hindi">दिव्य धाम निर्माण से लेकर समाज सेवा तक के पावन पल</p>
        </div>

        <!-- Gallery Filter -->
        <div class="text-center mb-4">
            <div class="btn-group btn-group-toggle" data-toggle="buttons">
                <button class="btn btn-outline-accent active filter-button" data-filter="all">
                    <span class="hindi">सभी</span>
                </button>
                <button class="btn btn-outline-accent filter-button" data-filter="construction">
                    <span class="hindi">निर्माण</span>
                </button>
                <button class="btn btn-outline-accent filter-button" data-filter="pooja">
                    <span class="hindi">पूजा-अर्चना</span>
                </button>
                <button class="btn btn-outline-accent filter-button" data-filter="seva">
                    <span class="hindi">सामाजिक सेवा</span>
                </button>
                <button class="btn btn-outline-accent filter-button" data-filter="events">
                    <span class="hindi">कार्यक्रम</span>
                </button>
            </div>
        </div>

        <!-- Gallery Grid -->
        <div class="row gallery-grid g-3">
            <!-- Item 1 -->
            <div class="col-6 col-md-4 col-lg-3 gallery-item construction" data-category="nirmaan">
                <div class="gallery-card" onclick="openModal(this)">
                    <img src="{{ asset('asset/images/w1.jpg') }}" alt="Temple Construction" class="img-fluid">
                    <div class="gallery-overlay">
                        <div class="overlay-content">
                            <h5 class="hindi">धाम निर्माण</h5>
                            <p class="small hindi">प्रथम चरण का कार्य प्रगति पर</p>
                            <i class="fas fa-search-plus overlay-icon"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Item 2 -->
            <div class="col-6 col-md-4 col-lg-3 gallery-item pooja" data-category="pooja">
                <div class="gallery-card" onclick="openModal(this)">
                    <img src="{{ asset('asset/images/w2.jpg') }}" alt="Shiv Puja" class="img-fluid">
                    <div class="gallery-overlay">
                        <div class="overlay-content">
                            <h5 class="hindi">शिव पूजा</h5>
                            <p class="small hindi">मासिक रुद्राभिषेक</p>
                            <i class="fas fa-search-plus overlay-icon"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Item 3 -->
            <div class="col-6 col-md-4 col-lg-3 gallery-item seva" data-category="seva">
                <div class="gallery-card" onclick="openModal(this)">
                    <img src="{{ asset('asset/images/w3.jpg') }}" alt="Food Donation" class="img-fluid">
                    <div class="gallery-overlay">
                        <div class="overlay-content">
                            <h5 class="hindi">अन्नदान</h5>
                            <p class="small hindi">महीने का 10वां दिन विशेष वितरण</p>
                            <i class="fas fa-search-plus overlay-icon"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Item 4 -->
            <div class="col-6 col-md-4 col-lg-3 gallery-item events" data-category="events">
                <div class="gallery-card" onclick="openModal(this)">
                    <img src="{{ asset('asset/images/w4.jpg') }}" alt="Religious Event" class="img-fluid">
                    <div class="gallery-overlay">
                        <div class="overlay-content">
                            <h5 class="hindi">शिवरात्रि</h5>
                            <p class="small hindi">जागरण एवं भंडारा</p>
                            <i class="fas fa-search-plus overlay-icon"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="imgModal" class="modal">
            <div class="modal-content-wrapper">
                <span class="close" onclick="closeModal()">&times;</span>
                <!-- <img src="{{ asset('images/shiv.jpeg') }}" id="modalImage" class="img-fluid" alt="Gallery Image">
                <img id="modalImage" src="" alt="Image"> -->
            </div>
        </div>

        <!-- View More Button -->
        <div class="text-center text-white mt-4">
            <a href="/gallery">
                <button class="btn btn-accent hindi">और छवियाँ देखें
                    <i class="fas fa-arrow-right ms-2"></i>
                </button>
            </a>
        </div>
    </div>
</section>

<!-- Lightbox Modal -->
<div class="modal fade" id="galleryModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title hindi" id="modalTitle">शीर्षक</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img src="{{ asset('asset/images/shiv.jpeg') }}" id="modalImage" class="img-fluid" alt="Gallery Image">
                <p class="mt-3 hindi" id="modalDesc">विवरण</p>
            </div>
        </div>
    </div>
</div>


<!-- Gallery Video Section -->
<div class="container mt-5">
    <div class="row g-4">
        <!-- Video 1 -->
        <div class="col-md-6">
            <div class="video-card">
                <div class="ratio ratio-16x9">
                    <iframe src="https://www.youtube.com/embed/AhfJLJU7h3Y?rel=0&modestbranding=1&controls=1"
                        title="Shivling Installation" allowfullscreen></iframe>
                </div>
                <div class="video-caption p-3">
                    <h5 class="hindi">शिवलिंग प्रतिष्ठा संस्कार</h5>
                    <p class="small hindi">प्रथम चरण के दौरान पंचामृत अभिषेक</p>
                </div>
            </div>
        </div>

        <!-- Video 2 -->
        <div class="col-md-6">
            <div class="video-card">
                <div class="ratio ratio-16x9">
                    <iframe width="560" height="315" src="https://www.youtube.com/embed/bjlNc5wVWRs?si=oS5y8N5IDcZp662J" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share" referrerpolicy="strict-origin-when-cross-origin" allowfullscreen></iframe>
                </div>
                <div class="video-caption p-3">
                    <h5 class="hindi">अन्नदान सेवा</h5>
                    <p class="small hindi">मासिक भंडारे का विशेष आयोजन</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Testimonial Gallery -->
<section class="py-5 bg-white">
    <div class="container">
        <h4 class="text-center mb-4 hindi">श्रद्धालुओं के अनुभव</h4>
        <div class="row g-3">
            <!-- Testimonial 1 -->
            <div class="col-md-4">
                <div class="testimonial-card p-3">
                    <div class="d-flex mb-3">
                        <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="User" class="rounded-circle me-3"
                            width="60">
                        <div>
                            <h5 class="mb-1 hindi">राजेश कुमार</h5>
                            <div class="text-warning mb-1">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                            </div>
                            <p class="small hindi">"शिवलिंग प्रतिष्ठा में भाग लेकर अद्भुत आध्यात्मिक अनुभव प्राप्त हुआ"</p>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Testimonial 2 -->
            <div class="col-md-4">
                <div class="testimonial-card p-3">
                    <div class="d-flex mb-3">
                        <img src="https://randomuser.me/api/portraits/women/43.jpg" alt="User" class="rounded-circle me-3"
                            width="60">
                        <div>
                            <h5 class="mb-1 hindi">प्रिया शर्मा</h5>
                            <div class="text-warning mb-1">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <p class="small hindi">"अन्नदान सेवा में भाग लेकर असीम संतुष्टि मिली"</p>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Testimonial 3 -->
            <div class="col-md-4">
                <div class="testimonial-card p-3">
                    <div class="d-flex mb-3">
                        <img src="https://randomuser.me/api/portraits/women/65.jpg" alt="User" class="rounded-circle me-3"
                            width="60">
                        <div>
                            <h5 class="mb-1 hindi">सुनीता देवी</h5>
                            <div class="text-warning mb-1">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                            <p class="small hindi">"बच्चों के शिक्षा कार्यक्रम से मेरा बेटा बहुत लाभान्वित हुआ"</p>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section - Rudra Narmadeshwar Seva Samiti -->
<section id="contact" class="py-5 bg-white">
    <div class="container">
        <div class="text-center mb-5">
            <div class="contact-icon mb-3">
                <i class="fas fa-om text-saffron fa-3x"></i>
            </div>
            <h2 class="section-heading hindi">हमसे जुड़ें</h2>
            <p class="lead hindi">आपके विचार एवं सहयोग हमारे लिए अमूल्य हैं</p>
        </div>

        <div class="row g-4">
            <!-- Contact Card - Left Side -->
            <div class="col-lg-5">
                <div class="contact-card p-4 h-100">
                    <div class="text-center mb-4">

                        <img src="https://cdn-icons-png.flaticon.com/512/2103/2103633.png" alt="Shiva Icon" width="80"
                            class="mb-3">
                        <h4 class="hindi">रूद्र नर्मदेश्वर सेवा समिति</h4>
                        <p>Serving the Divine Through Service</p>
                    </div>

                    <div class="contact-details">
                        <div class="d-flex mb-4">
                            <div class="flex-shrink-0 me-3">
                                <i class="fas fa-map-marker-alt fa-lg text-saffron"></i>
                            </div>
                            <div>
                                <h5 class="hindi">पता</h5>
                                <address class="hindi mb-0">
                                    बजरंग पारा, कोहका, भिलाई<br>
                                    गली नंबर - 18/a<br>
                                    छत्तीसगढ़ - 490023
                                </address>
                            </div>
                        </div>

                        <div class="d-flex mb-4">
                            <div class="flex-shrink-0 me-3">
                                <i class="fas fa-phone-alt fa-lg text-saffron"></i>
                            </div>
                            <div>
                                <h5 class="hindi">संपर्क</h5>
                                <p class="mb-1">
                                    <a href="tel:+919131843262" class="text-dark">+91 91318 43262</a>
                                </p>
                                <p class="hindi mb-0">सोमवार - शुक्रवार: 9:00 सुबह - 6:00 शाम</p>
                            </div>
                        </div>

                        <div class="d-flex mb-4">
                            <div class="flex-shrink-0 me-3">
                                <i class="fas fa-envelope fa-lg text-saffron"></i>
                            </div>
                            <div>
                                <h5 class="hindi">ईमेल</h5>
                                <p class="mb-0">
                                    <a href="mailto:<EMAIL> " class="text-dark"><EMAIL> </a><br>
                                    <a href="mailto:<EMAIL>" class="text-dark"><EMAIL></a>
                                </p>
                            </div>
                        </div>

                        <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                                <i class="fas fa-certificate fa-lg text-saffron"></i>
                            </div>
                            <div>
                                <h5 class="hindi">रजिस्ट्रेशन</h5>
                                <p class="mb-0 hindi">
                                    पंजीकरण संख्या: 122202484416<br>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <!-- Contact Form - Right Side -->
            <div class="col-lg-7">
                <div class="contact-form-card p-4">
                    <h4 class="text-center mb-4 hindi">संदेश भेजें</h4>
                    <form method="post" action="" id="contactForm">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" name="username" class="form-control" id="name" placeholder="Your Name" required>
                                    <label for="name" class="hindi">पूरा नाम *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="email" name="email" class="form-control" id="email" placeholder="Email" required>
                                    <label for="email" class="hindi">ईमेल *</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="tel" name="mobile" class="form-control" id="phone" placeholder="Phone">
                                    <label for="phone" class="hindi">फोन नंबर</label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select" id="subject" name="subject">
                                        <option value="general">सामान्य जानकारी</option>
                                        <option value="donation">दान सम्बंधित</option>
                                        <option value="volunteer">स्वयंसेवक बनना चाहते हैं</option>
                                        <option value="visit">धाम दर्शन की जानकारी</option>
                                    </select>
                                    <label for="subject" class="hindi">विषय</label>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-floating">
                                    <textarea name="message" class="form-control" id="message" style="height: 120px" required></textarea>
                                    <label for="message" class="hindi">आपका संदेश *</label>
                                </div>
                            </div>
                            <div class="col-12">
                                <button type="submit" name="contact" class="btn btn-accent w-100 py-2 hindi text-white">
                                    संदेश भेजें <i class="fas fa-paper-plane ms-2"></i>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Spiritual Message -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="spiritual-message text-center p-4">
                    <i class="fas fa-quote-left text-muted me-2"></i>
                    <span class="hindi">आपका प्रत्येक संदेश हमारे लिए शिव की कृपा के समान है। हम 24 घंटे के भीतर उत्तर देने का
                        प्रयास करेंगे।</span>
                    <i class="fas fa-quote-right text-muted ms-2"></i>
                </div>
            </div>
        </div>
    </div>
</section>



<script>
    document.getElementById("submitBtn").addEventListener("click", function() {
        const form = document.getElementById("donationForm");

        const name = document.getElementById('donorName').value.trim();
        const email = document.getElementById('donorEmail').value.trim();
        const mobile = document.getElementById('donorPhone').value.trim();
        const state = form.state.value;
        const city = form.city.value.trim();
        const address = form.address.value.trim();
        const amount = document.getElementById('amount').value.trim();
        const purpose = document.getElementById('donationPurpose').options[document.getElementById('donationPurpose').selectedIndex].text;

        // ✅ Step 1: Check Amount
        if (!amount) {
            alert("कृपया पहले राशि चुनें या दर्ज करें।");
            return;
        }

        // ✅ Step 2: Check Other Required Fields
        if (!name || !email || !mobile || !state || !city || !address) {
            alert("कृपया सभी आवश्यक जानकारी भरें।");
            return;
        }

        // ✅ Step 3: Thank You Alert
        alert(`धन्यवाद, ${name} जी!\nआपने ₹${amount} ("${purpose}") दान करने के लिए चुना है।`);

        // ✅ Step 4: Show Thank You Modal Popup with QR
        document.getElementById('thankYouTitle').innerText = `धन्यवाद, ${name} जी!`;
        document.getElementById('thankYouMessage').innerText = `आपने ₹${amount} ("${purpose}") दान करने के लिए चुना है। कृपया नीचे दिए गए QR कोड को स्कैन करके पेमेंट करें:`;
        document.getElementById("thankYouModal").style.display = "flex";

        // ✅ Step 5: Send to PHP using fetch
        const formData = new FormData(form);
        fetch("", {
            method: "POST",
            body: formData
        }).then(res => res.text()).then(data => {
            console.log("Form data submitted to PHP.");
        });
    });

    // ✅ Modal Close Button
    document.querySelector(".modal .close").addEventListener("click", function() {
        document.getElementById("thankYouModal").style.display = "none";
        document.getElementById("donationForm").reset();
        document.getElementById("displayAmount").innerText = "";
    });
</script>


<script>
    // Predefined tier selection
    document.querySelectorAll('.select-tier-btn').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const amount = this.getAttribute('data-amount');
            const type = this.getAttribute('data-type');

            document.getElementById('amount').value = amount;
            document.getElementById('donationType').value = type; // ✅ set donation type
            document.getElementById('displayAmount').innerText = "₹" + amount;
        });
    });

    // Custom amount selection
    document.getElementById('selectCustom').addEventListener('click', function(e) {
        e.preventDefault();
        let customAmt = document.getElementById('customAmount').value;

        if (customAmt > 0) {
            document.getElementById('amount').value = customAmt;
            document.getElementById('donationType').value = "अन्य राशि"; // ✅ force custom type
            document.getElementById('displayAmount').innerText = "₹" + customAmt;
        } else {
            alert("कृपया मान्य राशि दर्ज करें।");
        }
    });
</script>
<script>
    const tierButtons = document.querySelectorAll('.select-tier-btn');

    tierButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove highlight from all tiers
            document.querySelectorAll('.donation-tier').forEach(tier => {
                tier.classList.remove('active-tier');
                const tierBtn = tier.querySelector('.select-tier-btn');
                if (tierBtn) {
                    tierBtn.classList.remove('active');
                    tierBtn.textContent = 'चुनें';
                }
            });

            // Highlight selected tier
            const selectedTier = this.closest('.donation-tier');
            selectedTier.classList.add('active-tier');
            this.classList.add('active');
            this.textContent = 'चयनित';

            // Update hidden inputs and display amount
            const amount = this.getAttribute('data-amount');
            const type = this.getAttribute('data-type');

            document.getElementById('amount').value = amount.replace(/,/g, '');
            document.getElementById('donationType').value = type;
            document.getElementById('displayAmount').textContent = '₹' + amount;
        });
    });

    // For custom amount selection
    document.getElementById('selectCustom').addEventListener('click', function() {
        const customAmount = document.getElementById('customAmount').value;
        if (customAmount && parseInt(customAmount) > 0) {
            // Remove active from predefined tiers
            document.querySelectorAll('.donation-tier').forEach(tier => {
                tier.classList.remove('active-tier');
                const tierBtn = tier.querySelector('.select-tier-btn');
                if (tierBtn) {
                    tierBtn.classList.remove('active');
                    tierBtn.textContent = 'चुनें';
                }
            });

            document.getElementById('amount').value = customAmount;
            document.getElementById('donationType').value = 'अन्य राशि';
            document.getElementById('displayAmount').textContent = '₹' + customAmount;
        }
    });
</script>
<script>
    //  Gallery Script

    // Filter gallery items
    const buttons = document.querySelectorAll('.filter-button');
    const items = document.querySelectorAll('.gallery-item');

    buttons.forEach(btn => {
        btn.addEventListener('click', () => {
            // remove active class
            buttons.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');

            const filter = btn.getAttribute('data-filter');

            items.forEach(item => {
                if (filter === 'all' || item.classList.contains(filter)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    });
</script>

<!-- search button -->
<script>
    function openModal(wrapper) {
        const img = wrapper.querySelector("img");

        document.getElementById("modalImage").src = img.src;
        document.getElementById("imgModal").style.display = "block";
    }

    function closeModal() {
        document.getElementById("imgModal").style.display = "none";
    }
</script>

<!-- contact -->

<!-- ✅ JavaScript -->
<script>
    document.getElementById('contactForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const form = this;
        const formData = new FormData(form);

        fetch('contact_form.php', {
                method: 'POST',
                body: formData
            })
            .then(res => res.text())
            .then(result => {
                console.log('Response:', result);
                if (result.trim() === 'success') {
                    alert('✅ आपका संदेश सफलतापूर्वक भेजा गया है।');
                    form.reset();
                } else {
                    alert(result); // Show actual PHP/MySQL error
                }
            })
            .catch(err => {
                console.error(err);
                alert('❌ नेटवर्क त्रुटि।');
            });
    });
</script>

@include('web.footer')