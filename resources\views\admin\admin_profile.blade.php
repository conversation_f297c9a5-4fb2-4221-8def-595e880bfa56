<!DOCTYPE html>
<html lang="en">

<head>

    <style>
        :root {
            --light: #f4f4f4;
            --shiva-blue: #003366;
            --saffron: #ff9933;
            --white: #ffffff;
        }

        body {
            min-height: 100vh;
            background: var(--light);
            font-family: 'Poppins', sans-serif;
        }

        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 220px;
            background: var(--shiva-blue);
            color: var(--white);
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 15px rgba(0, 0, 0, 0.07);
            z-index: 10;
            transition: all 0.3s ease;
        }

        .sidebar-header {
            font-size: 1.5rem;
            font-weight: 700;
            padding: 32px 24px 24px 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
        }

        .sidebar-menu {
            flex: 1;
            padding: 24px 0;
        }

        .sidebar-menu a {
            display: block;
            color: var(--white);
            text-decoration: none;
            padding: 14px 32px;
            font-size: 1.1rem;
            border-radius: 4px 0 0 4px;
            margin-bottom: 8px;
            transition: background 0.2s;
        }

        .sidebar-menu a.active,
        .sidebar-menu a:hover {
            background: var(--saffron);
            color: var(--shiva-blue);
        }

        .main-content {
            margin-left: 220px;
            padding: 40px 32px;
        }

        .dashboard-header {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--shiva-blue);
            margin-bottom: 32px;
        }

        .profile-card {
            background-color: white;
            max-width: 450px;
            margin: 90px auto;
            border-radius: 16px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08);
            text-align: center;
            padding: 40px 30px;
        }

        .profile-circle {
            width: 100px;
            height: 100px;
            background: linear-gradient(to right, #0d6efd, #6610f2);
            color: white;
            font-size: 36px;
            font-weight: bold;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px auto;
        }

        .profile-name {
            font-size: 24px;
            font-weight: 600;
            color: #333;
        }

        .profile-email {
            color: #777;
            font-size: 15px;
            margin-bottom: 10px;
        }

        .badge-role {
            background-color: #0d6efd;
            padding: 6px 14px;
            border-radius: 20px;
            color: white;
            font-size: 13px;
        }

        .btn-custom {
            border-radius: 30px;
            padding: 10px 25px;
            font-size: 15px;
        }

        .btn-custom i {
            margin-right: 8px;
        }

        .btn-outline-primary:hover {
            background-color: #0d6efd;
            color: white;
        }

        .btn-outline-danger:hover {
            background-color: #dc3545;
            color: white;
        }

        .logo {
            width: 50px;
            height: 50px;
            margin-left: 40px;
        }
    </style>
</head>

<body>
    @include('admin.includes.header')
    <div class="container">
        <div class="profile-card">
            <div class="profile-circle">
               
            </div>
            <div class="profile-name"></div>
            <div class="profile-email"></div>
            <div class="mb-3"><span class="badge-role">Admin</span></div>
            <div class="d-grid gap-3 mt-4">
                <a href="/admin/edit_profile" class="btn btn-outline-primary btn-custom">
                    <i class="fas fa-user-edit"></i> प्रोफ़ाइल संपादित करें
                </a>
                <a href="/admin/logout" class="btn btn-outline-danger btn-custom">
                    <i class="fas fa-sign-out-alt"></i> लॉगआउट
                </a>
            </div>
        </div>
    </div>


</body>

</html>
@include('admin.includes.footer')