<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Admin;
use Illuminate\Support\Facades\Hash;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create super admin if it doesn't exist
        Admin::firstOrCreate(
            ['username' => 'admin'],
            [
                'name' => 'Super Administrator',
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'phone' => '+91-9876543210',
                'role' => 'super_admin',
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        );

        // Create additional admin users
        Admin::firstOrCreate(
            ['username' => 'moderator'],
            [
                'name' => 'Moderator',
                'username' => 'moderator',
                'email' => '<EMAIL>',
                'password' => Hash::make('mod123'),
                'phone' => '+91-9876543211',
                'role' => 'moderator',
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        );

        // Create NGO admin
        Admin::firstOrCreate(
            ['username' => 'ngo_admin'],
            [
                'name' => 'NGO Administrator',
                'username' => 'ngo_admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('ngo123'),
                'phone' => '+91-9876543212',
                'role' => 'admin',
                'status' => 'active',
                'email_verified_at' => now(),
            ]
        );
    }
}
